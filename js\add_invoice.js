// Initialize theme
const body = document.body;

// Set initial theme from window variable
if (window.userTheme === 'Dark') {
    body.classList.add('dark-mode');
}

document.addEventListener('DOMContentLoaded', function() {
            // دالة تشغيل الأصوات
            function playSound(soundName) {
                try {
                    const audio = new Audio(`sounds/${soundName}.wav`);
                    audio.volume = 0.5; // تحديد مستوى الصوت إلى 50%
                    audio.play().catch(error => {
                        console.log('Could not play sound:', error);
                        // في حالة فشل تشغيل الصوت، جرب الملف الاحتياطي
                        if (soundName === 'success') {
                            try {
                                const backupAudio = new Audio('sounds/notification.mp3');
                                backupAudio.volume = 0.5;
                                backupAudio.play();
                            } catch (backupError) {
                                console.log('Could not play backup sound:', backupError);
                            }
                        } else if (soundName === 'error') {
                            try {
                                const backupAudio = new Audio('sounds/notification.mp3');
                                backupAudio.volume = 0.7; // صوت أعلى قليلاً للأخطاء
                                backupAudio.play();
                            } catch (backupError) {
                                console.log('Could not play backup error sound:', backupError);
                            }
                        } else if (soundName === 'return') {
                            try {
                                const backupAudio = new Audio('sounds/notification.mp3');
                                backupAudio.volume = 0.6; // صوت متوسط للمرتجعات
                                backupAudio.play();
                            } catch (backupError) {
                                console.log('Could not play backup return sound:', backupError);
                            }
                        }
                    });
                } catch (error) {
                    console.log('Audio creation failed:', error);
                }
            }

            // جعل دالة تشغيل الصوت متاحة عالمياً
            window.playSound = playSound;

            // تكوين إعدادات Toastr
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "rtl": true
            };

            // التأكد من وجود window.addedItems وإنشاؤه إذا لم يكن موجوداً
            if (!window.addedItems) {
                window.addedItems = [];
            }
            const addedItems = window.addedItems;
            const itemCountElement = document.getElementById('item-count');
            const viewItemsButton = document.getElementById('view-items');
            const invoiceType = document.getElementById('invoice-type');
            const branchSelect = document.getElementById('branch-select');
            const buyerSelect = document.getElementById('account-select');
            const customerSelect = document.getElementById('customer-select');
            const saleOptions = document.getElementById('sale-options');
            const customerSaleOptions = document.getElementById('customer-sale-options');
            let capturedImages = []; // Store image files
            let currentFilter = 'all'; // Current filter state

            // صلاحيات المستخدم (تم تعيينها في add_invoice.php)
            const canCreatePurchase = window.canCreatePurchase || false;
            const canCreateWholesale = window.canCreateWholesale || false;

            // إضافة مستمع أحداث لاختصار الكيبورد (S/س) لحفظ الفاتورة
            document.addEventListener('keydown', function(e) {
                // التحقق من أن الضغط على حرف S أو s أو س مع عدم وجود مفاتيح إضافية (Ctrl, Alt, Shift)
                if ((e.key === 'S' || e.key === 's' || e.key === 'س') && !e.ctrlKey && !e.altKey && !e.shiftKey && !e.metaKey) {
                    // تجاهل الحدث إذا كان المستخدم يكتب في حقل إدخال
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT' || e.target.contentEditable === 'true') {
                        return;
                    }

                    e.preventDefault(); // منع السلوك الافتراضي

                    // محاكاة الضغط على زر حفظ الفاتورة
                    if (viewItemsButton && !viewItemsButton.disabled) {
                        // إضافة تأثير بصري للزر لإظهار أنه تم تفعيله
                        viewItemsButton.style.boxShadow = '0 0 20px #007bff';
                        viewItemsButton.style.transform = 'scale(0.95)';
                        viewItemsButton.style.backgroundColor = '#0056b3';

                        // إظهار إشعار صغير للمستخدم
                        showKeyboardShortcutNotification('تم استخدام اختصار حفظ الفاتورة (S/س)');

                        // تفعيل الزر بعد تأخير قصير لإظهار التأثير البصري
                        setTimeout(() => {
                            viewItemsButton.click();
                        }, 150);

                        // إزالة التأثير البصري بعد 300ms
                        setTimeout(() => {
                            viewItemsButton.style.boxShadow = '';
                            viewItemsButton.style.transform = '';
                            viewItemsButton.style.backgroundColor = '';
                        }, 300);

                        console.log('تم تفعيل حفظ الفاتورة باستخدام اختصار الكيبورد (S/س)');
                    }
                }

                // اختصار المساعدة (مفتاح F1)
                if (e.key === 'F1') {
                    e.preventDefault(); // منع فتح المساعدة الافتراضية للمتصفح
                    showKeyboardShortcutsHelp();
                }
            });

            // دالة لإظهار إشعار اختصار الكيبورد
            function showKeyboardShortcutNotification(message) {
                // إنشاء عنصر الإشعار
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    left: 20px;
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 10000;
                    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                    transform: translateX(-100%);
                    transition: transform 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-family: 'Cairo', sans-serif;
                `;

                notification.innerHTML = `
                    <i class="fas fa-keyboard"></i>
                    ${message}
                `;

                document.body.appendChild(notification);

                // إظهار الإشعار
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                // إخفاء الإشعار بعد 2.5 ثانية
                setTimeout(() => {
                    notification.style.transform = 'translateX(-100%)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 2500);
            }

            // دالة لإخفاء لوحة إرشادات اختصارات الكيبورد
            function hideShortcutsPanel() {
                const panel = document.getElementById('shortcuts-panel');
                if (panel) {
                    panel.style.transform = 'translateY(-100%)';
                    panel.style.opacity = '0';
                    setTimeout(() => {
                        panel.style.display = 'none';
                        // حفظ حالة الإخفاء في localStorage
                        localStorage.setItem('keyboardShortcutsHidden', 'true');
                    }, 300);
                }
            }

            // فحص حالة إظهار/إخفاء لوحة الإرشادات عند تحميل الصفحة
            function checkShortcutsPanelVisibility() {
                const panel = document.getElementById('shortcuts-panel');
                const isHidden = localStorage.getItem('keyboardShortcutsHidden') === 'true';

                if (panel && isHidden) {
                    panel.style.display = 'none';
                }
            }

            // تشغيل فحص حالة اللوحة عند تحميل الصفحة
            checkShortcutsPanelVisibility();

            // دالة لإظهار نافذة المساعدة مع جميع اختصارات الكيبورد
            function showKeyboardShortcutsHelp() {
                Swal.fire({
                    title: '<i class="fas fa-keyboard"></i> اختصارات الكيبورد',
                    html: `
                        <div style="text-align: right; font-family: 'Cairo', sans-serif;">
                            <div style="margin-bottom: 20px;">
                                <h4 style="color: #007bff; margin-bottom: 15px;"><i class="fas fa-save"></i> أوامر الحفظ:</h4>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                    <strong style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">S</strong>
                                    حفظ الفاتورة الحالية
                                </div>
                            </div>
                            
                            <div style="margin-bottom: 20px;">
                                <h4 style="color: #28a745; margin-bottom: 15px;"><i class="fas fa-search"></i> أوامر البحث:</h4>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                    <strong>Enter</strong> + <strong>الباركود</strong> - إضافة صنف بالباركود
                                </div>
                            </div>
                            
                            <div style="margin-bottom: 20px;">
                                <h4 style="color: #6f42c1; margin-bottom: 15px;"><i class="fas fa-question-circle"></i> أوامر المساعدة:</h4>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                    <strong style="background: #6f42c1; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">F1</strong>
                                    إظهار هذه النافذة
                                </div>
                            </div>
                            
                            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; color: #856404;">
                                <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                                <strong>ملاحظة:</strong> الاختصارات تعمل فقط عندما لا تكون تكتب في حقول الإدخال
                            </div>
                        </div>
                    `,
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#007bff',
                    width: '600px',
                    customClass: {
                        popup: 'keyboard-shortcuts-modal'
                    }
                });
            }

            // Register service worker for offline support
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/sw.js')
                        .then(registration => {
                            console.log('Service Worker registered with scope:', registration.scope);
                        })
                        .catch(error => {
                            console.error('Service Worker registration failed:', error);
                        });
                });
            }

            // تهيئة نظام العمل بدون اتصال
            initOfflineSystem().then(initialized => {
                if (initialized) {
                    console.log('Offline system initialized successfully');
                } else {
                    console.error('Failed to initialize offline system');
                }
            });

            // دالة لإعادة تعيين نموذج الفاتورة
            function resetInvoiceForm() {
                // إعادة تعيين الأصناف المضافة
                addedItems.length = 0;

                // إعادة تعيين جميع العناصر المحددة
                document.querySelectorAll('tr.highlight').forEach(row => {
                    const itemId = row.getAttribute('data-item-id');
                    const itemName = row.getAttribute('data-item-name');

                    row.classList.remove('highlight');
                    const nameCell = row.querySelector('td:first-child');
                    const cell = row.querySelector('.add-cell');

                    if (nameCell) {
                        const isFavorite = row.getAttribute('data-is-favorite') === '1';
                        nameCell.innerHTML = `
                            <div class="item-name">
                                <i class="fas fa-box-open text-primary item-icon"></i>
                                ${itemName}
                                ${isFavorite ? '<i class="fas fa-star text-warning" style="margin-right: 8px;" title="منتج مفضل"></i>' : ''}
                            </div>
                        `;
                    }

                    if (cell) {
                        cell.innerHTML = `
                            <button class="btn btn-primary add-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        `;

                        // إعادة ربط حدث الإضافة
                        cell.querySelector('.add-btn').addEventListener('click', function() {
                            addItem(row, itemId, itemName);
                        });
                    }
                });

                // تحديث العناصر في التصميم الريسبونسيف
                const selectedItemsTable = document.getElementById('selectedItemsTable');
                if (selectedItemsTable) {
                    selectedItemsTable.innerHTML = '';
                }

                // تحديث إجمالي الفاتورة في التصميم الريسبونسيف
                const totalAmountValue = document.getElementById('total_amount_value');
                if (totalAmountValue) {
                    totalAmountValue.textContent = '0.00';
                }

                // تحديث عدد العناصر
                const itemCountElement = document.getElementById('item-count');
                if (itemCountElement) {
                    itemCountElement.textContent = '0';
                }

                // تحديث التصميم الريسبونسيف إذا كان متاحاً
                if (typeof updateItemCount === 'function') {
                    updateItemCount();
                }

                // استرجاع الحالة الفارغة في التصميم الريسبونسيف
                if (typeof restoreSavedItemsResponsive === 'function') {
                    restoreSavedItemsResponsive();
                }

                // تنظيف الصور المحفوظة
                capturedImages.length = 0;

                // حفظ الحالة الجديدة
                saveItemsToFile();
            }

            // دالة لتحديد ما إذا كنا في الوضع الريسبونسيف أم لا
            function isResponsiveMode() {
                return window.innerWidth < 768;
            }

            // جعل الدالة متاحة عالمياً
            window.isResponsiveMode = isResponsiveMode;

            // دالة لعرض رسائل النجاح
            function showSuccessMessage(message, callback, forceToastr = false) {
                if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return' || forceToastr || isResponsiveMode()) {
                    // استخدام Toastr للفواتير المحددة أو في الوضع الريسبونسيف
                    toastr.success(message);
                    if (callback) setTimeout(callback, 1500);
                } else {
                    // استخدام SweetAlert في باقي الحالات
                    Swal.fire('تم الحفظ', message, 'success').then(callback);
                }
            }

            // دالة لعرض رسائل الخطأ
            function showErrorMessage(title, message, forceToastr = false) {
                if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return' || forceToastr || isResponsiveMode()) {
                    toastr.error(message, title);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: title,
                        text: message,
                        confirmButtonText: 'حسنًا'
                    });
                }
            }

            // دالة تشغيل صوت الخطأ
            function playErrorSound() {
                try {
                    const audio = new Audio('sounds/error.wav');
                    audio.volume = 0.7; // تحديد مستوى الصوت
                    audio.play().catch(error => {
                        console.log('لا يمكن تشغيل الصوت:', error);
                    });
                } catch (error) {
                    console.log('خطأ في تشغيل الصوت:', error);
                }
            }

            // دالة لعرض رسائل التحذير
            function showWarningMessage(title, message, forceToastr = false) {
                if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return' || forceToastr || isResponsiveMode()) {
                    toastr.warning(message, title);
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: title,
                        text: message,
                        confirmButtonText: 'حسنًا'
                    });
                }
            }

            // Restore saved items
            function restoreSavedItems() {
                addedItems.forEach(item => {
                    const row = document.querySelector(`tr[data-item-id="${item.id}"]`);
                    if (row) {
                        const nameCell = row.querySelector('td:first-child');
                        const cell = row.querySelector('.add-cell');

                        row.classList.add('highlight');
                        nameCell.innerHTML = `
                            <div class="item-name">
                                <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                                ${item.name}
                            </div>
                        `;
                        cell.innerHTML = `
                            <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="${item.quantity}" style="width: 80px; margin: auto;">
                        `;

                        // Update view images button for highlighted rows
                        const viewImagesCell = row.querySelector('td:nth-child(2)');
                        const viewImagesBtn = viewImagesCell.querySelector('.view-images-btn');
                        if (viewImagesBtn) {
                            viewImagesBtn.style.opacity = '0.7';
                            viewImagesBtn.style.pointerEvents = 'auto';
                        }

                        // Attach event listeners for quantity change and item removal
                        attachRowEvents(row, item);
                    }
                });

                // Update item count after restoring items
                itemCountElement.textContent = addedItems.length;

                // تحديث عداد الأصناف في الوضع الريسبونسيف
                const responsiveItemCount = document.getElementById('responsive-item-count');
                if (responsiveItemCount) {
                    responsiveItemCount.textContent = addedItems.length;
                }
            }

            // Attach event listeners to a row
            function attachRowEvents(row, item) {
                const nameCell = row.querySelector('td:first-child');
                const cell = row.querySelector('.add-cell');

                // Handle quantity change
                const quantityInput = cell.querySelector('.quantity-input');
                quantityInput.addEventListener('input', function() {
                    const savedItem = addedItems.find(savedItem => savedItem.id === item.id);
                    if (savedItem) {
                        savedItem.quantity = parseFloat(this.value) || 1;
                        saveItemsToFile();
                    }
                });

                // Handle item removal
                const removeIcon = nameCell.querySelector('.remove-item');
                removeIcon.addEventListener('click', function() {
                    const index = addedItems.findIndex(savedItem => savedItem.id === item.id);
                    if (index !== -1) {
                        addedItems.splice(index, 1);
                        saveItemsToFile();
                    }

                    row.classList.remove('highlight');
                    const isFavorite = row.getAttribute('data-is-favorite') === '1';
                    nameCell.innerHTML = `
                        <div class="item-name">
                            <i class="fas fa-box-open text-primary item-icon"></i>
                            ${item.name}
                            ${isFavorite ? '<i class="fas fa-star text-warning" style="margin-right: 8px;" title="منتج مفضل"></i>' : ''}
                        </div>
                    `;
                    cell.innerHTML = `
                        <button class="btn btn-primary add-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    `;

                    // Restore view images button state
                    const viewImagesCell = row.querySelector('td:nth-child(2)');
                    const viewImagesBtn = viewImagesCell.querySelector('.view-images-btn');
                    if (viewImagesBtn) {
                        viewImagesBtn.style.opacity = '1';
                        viewImagesBtn.style.pointerEvents = 'auto';
                    }

                    // Update item count
                    itemCountElement.textContent = addedItems.length;

                    // تحديث عداد الأصناف في الوضع الريسبونسيف
                    const responsiveItemCount = document.getElementById('responsive-item-count');
                    if (responsiveItemCount) {
                        responsiveItemCount.textContent = addedItems.length;
                    }

                    // Reattach the click event to the "+" button
                    cell.querySelector('.add-btn').addEventListener('click', function() {
                        addItem(row, item.id, item.name);
                    });
                });
            }

            // Add an item to the list
            function addItem(row, itemId, itemName) {
                // فحص الصلاحيات أولاً
                if (invoiceType.value === 'purchase' && !canCreatePurchase) {
                    showErrorMessage('ليس لديك صلاحية', 'ليس لديك صلاحية لإنشاء فواتير الشراء');
                    return;
                }

                if ((invoiceType.value === 'sale' || invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') && !canCreateWholesale) {
                    showErrorMessage('ليس لديك صلاحية', 'ليس لديك صلاحية لإنشاء فواتير البيع');
                    return;
                }

                // Prevent adding items if invoice type is "sale" and branch or buyer is not selected
                if (invoiceType.value === 'sale') {
                    if (!branchSelect.value || !buyerSelect.value) {
                        showWarningMessage('يرجى اختيار الفرع المشتري والشخص المشتري', 'لا يمكنك إضافة أصناف إلى الفاتورة قبل تحديد الفرع المشتري والشخص المشتري.');
                        return;
                    }
                }

                // Prevent adding items if invoice type is customer sale/return and customer is not selected
                if ((invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') && !customerSelect.value) {
                    showWarningMessage('يرجى اختيار العميل', 'لا يمكنك إضافة أصناف إلى الفاتورة قبل تحديد العميل.');
                    return;
                }

                // Check if the item is already added
                const existingItemIndex = addedItems.findIndex(item => item.id === itemId);
                if (existingItemIndex !== -1) {
                    // زيادة الكمية بدلاً من إظهار رسالة تنبيه
                    addedItems[existingItemIndex].quantity += 1;

                    // التأكد من وجود السعر، وإضافته إذا لم يكن موجودًا
                    if (!addedItems[existingItemIndex].price) {
                        const itemPrice = row.getAttribute('data-item-price');
                        addedItems[existingItemIndex].price = itemPrice;
                    }

                    // تحديث الكمية في الواجهة
                    const existingRow = document.querySelector(`tr[data-item-id="${itemId}"]`);
                    if (existingRow) {
                        const quantityInput = existingRow.querySelector('.quantity-input');
                        if (quantityInput) {
                            quantityInput.value = addedItems[existingItemIndex].quantity;
                            // إطلاق حدث input لتحديث الإجمالي
                            quantityInput.dispatchEvent(new Event('input'));
                        }
                    }

                    // إظهار رسالة نجاح للكمية المحدثة
                    if (isResponsiveMode() || invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                        toastr.success(`تم زيادة كمية ${itemName} إلى ${addedItems[existingItemIndex].quantity}`);
                    }

                    saveItemsToFile();
                    return;
                }

                // Add the item
                const itemPrice = row.getAttribute('data-item-price');
                addedItems.push({
                    id: itemId,
                    name: itemName,
                    quantity: 1,
                    price: itemPrice,
                    isCustomBarcode: false,
                    customBarcodeData: null
                });
                row.classList.add('highlight');
                const nameCell = row.querySelector('td:first-child');
                const cell = row.querySelector('.add-cell');
                nameCell.innerHTML = `
                    <div class="item-name">
                        <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                        ${itemName}
                    </div>
                `;
                cell.innerHTML = `
                    <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="1" style="width: 80px; margin: auto;">
                `;

                // Update view images button for highlighted rows
                const viewImagesCell = row.querySelector('td:nth-child(2)');
                const viewImagesBtn = viewImagesCell.querySelector('.view-images-btn');
                if (viewImagesBtn) {
                    viewImagesBtn.style.opacity = '0.7';
                    viewImagesBtn.style.pointerEvents = 'auto';
                }

                itemCountElement.textContent = addedItems.length;

                // تحديث عداد الأصناف في الوضع الريسبونسيف
                const responsiveItemCount = document.getElementById('responsive-item-count');
                if (responsiveItemCount) {
                    responsiveItemCount.textContent = addedItems.length;
                }

                // إظهار رسالة نجاح لإضافة صنف جديد
                if (isResponsiveMode() || invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                    toastr.success(`تم إضافة ${itemName} للفاتورة`);
                }

                saveItemsToFile();

                // إضافة معالج أحداث للكمية
                const quantityInput = row.querySelector('.quantity-input');
                if (quantityInput) {
                    quantityInput.addEventListener('input', function() {
                        // تحديث الكمية في addedItems
                        const itemIndex = addedItems.findIndex(item => item.id === itemId);
                        if (itemIndex !== -1) {
                            addedItems[itemIndex].quantity = parseFloat(this.value) || 1;
                        }

                        // حفظ التغييرات
                        saveItemsToFile();
                    });
                }
            }

            // Save items to the JSON file with debouncing for better performance
            let saveTimeout = null;

            function saveItemsToFile() {
                // إلغاء الحفظ السابق إذا كان معلقاً
                if (saveTimeout) {
                    clearTimeout(saveTimeout);
                }

                // تأخير الحفظ لتجميع التغييرات المتعددة
                saveTimeout = setTimeout(() => {
                    const encryptedAccountId = window.encryptedAccountId;
                    const params = new URLSearchParams();
                    params.append('account_id', encryptedAccountId);
                    params.append('items', JSON.stringify(addedItems));
                    params.append('invoice_type', invoiceType.value);

                    // Include branch and buyer for "sale" invoices
                    if (invoiceType.value === 'sale') {
                        params.append('branch_id', branchSelect.value);
                        params.append('account_buyer_id', buyerSelect.value);
                    }

                    // Include customer for customer sale/return invoices
                    if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                        params.append('account_buyer_id', customerSelect.value);
                    }

                    const url = `save_invoice.php?${params.toString()}`;
                    fetch(url, { method: 'GET' }).catch(error => {
                        console.error('Error saving items:', error);
                    });
                }, 300); // تأخير 300 مللي ثانية
            }

            // Filter function
            function applyFilter(filterType) {
                currentFilter = filterType;
                const rows = document.querySelectorAll('.custom-table tbody tr:not(.no-results-row)');
                let visibleCount = 0;

                rows.forEach(row => {
                    const itemId = row.getAttribute('data-item-id');
                    if (!itemId) return; // Skip non-item rows

                    const imageCount = parseInt(row.getAttribute('data-image-count') || 0);
                    const isFavorite = row.getAttribute('data-is-favorite') === '1';
                    let shouldShow = true;

                    switch (filterType) {
                        case 'all':
                            shouldShow = true;
                            break;
                        case 'favorites':
                            shouldShow = isFavorite;
                            break;
                        case 'with-images':
                            shouldShow = imageCount > 0;
                            break;
                        case 'no-images':
                            shouldShow = imageCount === 0;
                            break;
                    }

                    if (shouldShow) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Update filter buttons
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

                // Show no results message if needed
                showNoResultsMessage(visibleCount, filterType);
            }

            function showNoResultsMessage(visibleCount, filterType) {
                let noResultsRow = document.querySelector('.no-results-row');

                if (visibleCount === 0) {
                    if (!noResultsRow) {
                        let message = '';
                        switch (filterType) {
                            case 'favorites':
                                message = 'لا توجد أصناف في المفضلة';
                                break;
                            case 'with-images':
                                message = 'لا توجد أصناف لها صور';
                                break;
                            case 'no-images':
                                message = 'جميع الأصناف لها صور';
                                break;
                            default:
                                message = 'لا توجد أصناف متاحة';
                        }

                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="4" class="text-center" style="padding: 40px 20px; color: var(--secondary-color);">
                                <i class="fas fa-filter" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;"></i>
                                <div style="font-size: 1.1rem; font-weight: 600;">${message}</div>
                                <div style="font-size: 0.9rem; margin-top: 5px;">جرب فلتر آخر أو أضف منتجات جديدة</div>
                            </td>
                        `;
                        document.querySelector('.custom-table tbody').appendChild(noResultsRow);
                    }
                } else if (noResultsRow) {
                    noResultsRow.remove();
                }
            }

            // Restore saved items on page load
            restoreSavedItems();

            // Initialize offline system if available
            if (typeof initOfflineSystem === 'function') {
                initOfflineSystem().then(initialized => {
                    if (initialized) {
                        console.log('Offline system initialized successfully');
                    } else {
                        console.log('Offline system not available');
                    }
                }).catch(error => {
                    console.log('Offline system error:', error);
                });
            }

            // Handle adding items
            document.querySelectorAll('.add-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const row = this.closest('tr');
                    const itemId = row.getAttribute('data-item-id');
                    const itemName = row.getAttribute('data-item-name');
                    addItem(row, itemId, itemName);
                });
            });

            // Handle view images buttons
            document.querySelectorAll('.view-images-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.getAttribute('data-item-id');
                    const row = this.closest('tr');
                    const itemName = row.getAttribute('data-item-name');
                    viewItemImages(itemId, itemName);
                });
            });

            // Handle filter buttons
            document.querySelectorAll('.filter-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const filterType = this.getAttribute('data-filter');
                    applyFilter(filterType);
                });
            });

            // Create FormData for submission
            function createFormData(data) {
                const formData = new FormData();
                formData.append('store_id', data.store_id);
                formData.append('account_id', data.account_id);
                if (data.branch_id) formData.append('branch_id', data.branch_id);
                if (data.account_buyer_id) formData.append('account_buyer_id', data.account_buyer_id);
                formData.append('items', JSON.stringify(data.items));
                data.images.forEach((path, i) => formData.append(`images[${i}]`, path));
                return formData;
            }

            // Compress image before upload
            async function compressImage(file) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        const maxDim = 800;
                        let { width, height } = img;
                        if (width > height && width > maxDim) {
                            height = height * (maxDim / width);
                            width = maxDim;
                        } else if (height > maxDim) {
                            width = width * (maxDim / height);
                            height = maxDim;
                        }
                        const canvas = document.createElement('canvas');
                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);
                        canvas.toBlob(blob => {
                            resolve(new File([blob], file.name, { type: 'image/jpeg' }));
                        }, 'image/jpeg', 0.75); // Compress to 75% quality
                    };
                    img.src = URL.createObjectURL(file);
                });
            }

            // Upload images in parallel
            async function uploadImagesInParallel(images, type) {
                const compressedImages = await Promise.all(images.map(file => compressImage(file)));
                const uploadPromises = compressedImages.map((file) => {
                    const formData = new FormData();
                    formData.append('image', file);
                    formData.append('type', type); // Include invoice type
                    return fetch('upload_image.php', {
                        method: 'POST',
                        body: formData
                    }).then(res => res.json());
                });

                return Promise.all(uploadPromises);
            }

            // Handle viewing items
            viewItemsButton.addEventListener('click', async function() {
                if (addedItems.length === 0) {
                    Swal.fire('لا توجد أصناف مضافة', 'يرجى إضافة أصناف أولاً.', 'info');
                    return;
                }

                // للمبيعات للعملاء ومرتجع العملاء، حفظ مباشر بدون معاينة أو تحميل صور
                if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                    if (!customerSelect.value) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'يرجى اختيار العميل',
                            text: 'لا يمكنك حفظ الفاتورة قبل تحديد العميل.',
                            confirmButtonText: 'حسناً'
                        });
                        return;
                    }

                    // إنشاء بيانات الفاتورة للمبيعات للعملاء
                    const invoiceData = {
                        type: invoiceType.value,
                        store_id: window.encryptedStoreId,
                        account_id: window.encryptedAccountId,
                        account_buyer_id: window.encryptedAccountsMap[customerSelect.value],
                        items: addedItems,
                        images: []
                    };

                    try {
                        // استخدام دالة الحفظ مع دعم العمل دون اتصال
                        const result = await saveInvoiceWithOfflineSupport(invoiceData);

                        if (result.success) {
                            // تشغيل الصوت المناسب حسب نوع الفاتورة
                            if (window.playSound) {
                                if (invoiceType.value === 'customer_return') {
                                    console.log('تشغيل صوت مرتجع العملاء');
                                    window.playSound('return');
                                } else {
                                    window.playSound('success');
                                }
                            }

                            // تنفيذ عملية التحديث فوراً
                            resetInvoiceForm();

                            // عرض رسالة النجاح
                            showSuccessMessage(result.message, null, true);
                        } else {
                            // تشغيل صوت الخطأ
                            if (window.playSound) {
                                window.playSound('error');
                            }
                            Swal.fire('خطأ', result.message || 'حدث خطأ أثناء حفظ الفاتورة.', 'error');
                        }
                    } catch (error) {
                        console.error("Error saving customer invoice:", error);
                        // تشغيل صوت الخطأ
                        if (window.playSound) {
                            window.playSound('error');
                        }
                        Swal.fire('خطأ', 'حدث خطأ أثناء حفظ الفاتورة.', 'error');
                    }

                    return;
                }

                let tableContent = `
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-box" style="margin-left: 8px;"></i>
                                        اسم المنتج
                                    </th>
                                    <th>
                                        <i class="fas fa-sort-numeric-up" style="margin-left: 8px;"></i>
                                        الكمية
                                    </th>
                                    <th>
                                        <i class="fas fa-trash" style="margin-left: 8px;"></i>
                                        حذف
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                addedItems.forEach((item, index) => {
                    tableContent += `
                        <tr data-item-index="${index}">
                            <td>
                                <div class="item-name">
                                    <i class="fas fa-box-open text-primary item-icon"></i>
                                    ${item.name}
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control modal-quantity-input" 
                                       value="${item.quantity}" min="0.1" step="0.1" 
                                       data-item-index="${index}" 
                                       style="width: 80px; text-align: center;">
                            </td>
                            <td class="text-center">
                                <button class="btn btn-danger btn-sm delete-item-btn" data-item-index="${index}" title="حذف الصنف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
                tableContent += `
                            </tbody>
                        </table>
                    </div>
                `;

                Swal.fire({
                    title: 'الأصناف المضافة',
                    html: `
                        ${tableContent}
                        <button id="upload-file" class="btn btn-secondary mt-3">
                            <i class="fas fa-upload"></i> تحميل ملف
                        </button>
                        <div id="image-gallery"></div>
                        <input type="file" id="file-input" accept="image/*" multiple style="display: none;">
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'تأكيد',
                    cancelButtonText: 'استكمال الاصناف',
                    customClass: {
                        container: 'custom-swal-products'
                    },
                    backdrop: true,
                    allowOutsideClick: false,
                    allowEscapeKey: true,
                    showClass: {
                        popup: 'swal2-show'
                    },
                    hideClass: {
                        popup: 'swal2-hide'
                    },
                    didOpen: () => {
                        const uploadFileButton = Swal.getPopup().querySelector('#upload-file');
                        const fileInput = Swal.getPopup().querySelector('#file-input');
                        const imageGallery = Swal.getPopup().querySelector('#image-gallery');

                        uploadFileButton.addEventListener('click', () => fileInput.click());
                        fileInput.addEventListener('change', (event) => {
                            const files = event.target.files;
                            Array.from(files).forEach(file => {
                                capturedImages.push(file);
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                    const thumbnail = document.createElement('div');
                                    thumbnail.style.position = 'relative';
                                    thumbnail.style.width = '80px';
                                    thumbnail.style.height = '80px';
                                    thumbnail.style.border = '1px solid #ddd';
                                    thumbnail.style.borderRadius = '5px';
                                    thumbnail.style.overflow = 'hidden';
                                    thumbnail.innerHTML = `
                                        <img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">
                                        <i class="fas fa-trash text-danger" style="position: absolute; top: 5px; right: 5px; cursor: pointer; background: white; border-radius: 50%; padding: 5px;"></i>
                                    `;
                                    thumbnail.querySelector('.fa-trash').addEventListener('click', () => {
                                        capturedImages = capturedImages.filter(img => img !== file);
                                        thumbnail.remove();
                                    });
                                    imageGallery.appendChild(thumbnail);
                                };
                                reader.readAsDataURL(file);
                            });
                        });

                        // معالجة تحديث الكمية في النافذة المنبثقة
                        const quantityInputs = Swal.getPopup().querySelectorAll('.modal-quantity-input');
                        quantityInputs.forEach(input => {
                            input.addEventListener('input', function() {
                                const itemIndex = parseInt(this.getAttribute('data-item-index'));
                                const newQuantity = parseFloat(this.value) || 0.1;

                                if (newQuantity >= 0.1) {
                                    // تحديث الكمية في المصفوفة
                                    addedItems[itemIndex].quantity = newQuantity;

                                    // تحديث الكمية في الجدول الأساسي
                                    const mainRow = document.querySelector(`tr[data-item-id="${addedItems[itemIndex].id}"]`);
                                    if (mainRow) {
                                        const mainQuantityInput = mainRow.querySelector('.quantity-input');
                                        if (mainQuantityInput) {
                                            mainQuantityInput.value = newQuantity;
                                        }
                                    }

                                    // حفظ التغييرات
                                    saveItemsToFile();
                                } else {
                                    // إعادة تعيين القيمة إذا كانت أقل من الحد الأدنى
                                    this.value = 0.1;
                                }
                            });
                        });

                        // معالجة أزرار الحذف في ��لجدول
                        const deleteButtons = Swal.getPopup().querySelectorAll('.delete-item-btn');
                        deleteButtons.forEach(button => {
                            button.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();

                                const itemIndex = parseInt(button.getAttribute('data-item-index'));
                                const itemToDelete = addedItems[itemIndex];

                                Swal.fire({
                                    title: 'تأكيد الحذف',
                                    text: `هل تريد حذف "${itemToDelete.name}" من الفاتورة؟`,
                                    icon: 'warning',
                                    showCancelButton: true,
                                    confirmButtonText: 'نعم، احذف',
                                    cancelButtonText: 'إلغاء',
                                    confirmButtonColor: '#dc3545',
                                    cancelButtonColor: '#6c757d'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        // حذف الصنف من المصفوفة
                                        addedItems.splice(itemIndex, 1);

                                        // تحديث عدد الأصناف
                                        itemCountElement.textContent = addedItems.length;

                                        // حفظ التغييرات
                                        saveItemsToFile();

                                        // إزالة highlight من الصف في الجدول الأساسي
                                        const originalRow = document.querySelector(`tr[data-item-id="${itemToDelete.id}"]`);
                                        if (originalRow) {
                                            originalRow.classList.remove('highlight');
                                            const nameCell = originalRow.querySelector('td:first-child');
                                            const addCell = originalRow.querySelector('.add-cell');

                                            const isFavorite = originalRow.getAttribute('data-is-favorite') === '1';
                                            nameCell.innerHTML = `
                                                <div class="item-name">
                                                    <i class="fas fa-box-open text-primary item-icon"></i>
                                                    ${itemToDelete.name}
                                                    ${isFavorite ? '<i class="fas fa-star text-warning" style="margin-right: 8px;" title="منتج مفضل"></i>' : ''}
                                                </div>
                                            `;
                                            addCell.innerHTML = `
                                            <button class="btn btn-primary add-btn">
                                            <i class="fas fa-plus"></i>
                                            </button>
                                            `;

                                            // Restore view images button state
                                            const viewImagesCell = originalRow.querySelector('td:nth-child(2)');
                                            const viewImagesBtn = viewImagesCell.querySelector('.view-images-btn');
                                            if (viewImagesBtn) {
                                                viewImagesBtn.style.opacity = '1';
                                                viewImagesBtn.style.pointerEvents = 'auto';
                                            }

                                            // إعادة ربط حدث الإضافة
                                            addCell.querySelector('.add-btn').addEventListener('click', function() {
                                                addItem(originalRow, itemToDelete.id, itemToDelete.name);
                                            });
                                        }

                                        // إغلاق النافذة الحالية وإعادة فتحها بالبيانات المحدثة
                                        Swal.close();

                                        // إعادة فتح النافذة إذا كان هناك أصناف متبقية
                                        if (addedItems.length > 0) {
                                            setTimeout(() => {
                                                viewItemsButton.click();
                                            }, 300);
                                        }

                                        Swal.fire({
                                            icon: 'success',
                                            title: 'تم الحذف',
                                            text: 'تم حذف الصنف من الفاتورة بنجاح',
                                            timer: 2000,
                                            timerProgressBar: true,
                                            showConfirmButton: false
                                        });
                                    }
                                });
                            });
                        });
                    },
                    preConfirm: async() => {
                        if (capturedImages.length === 0) {
                            Swal.showValidationMessage('يجب تحميل صورة واحدة على الأقل.');
                            return false;
                        }

                        try {
                            const invoiceTypeValue = invoiceType.value;
                            const uploadedImagePaths = await uploadImagesInParallel(capturedImages, invoiceTypeValue);

                            // Check if image upload failed
                            if (uploadedImagePaths.some(res => res.error)) {
                                Swal.showValidationMessage('فشل في تحميل بعض الصور. يرجى المحاولة مرة أخرى.');
                                return false;
                            }

                            const branchId = invoiceTypeValue === 'sale' ? branchSelect.value : null;
                            const accountBuyerId = invoiceTypeValue === 'sale' ? buyerSelect.value : null;

                            const response = await fetch(invoiceTypeValue === 'sale' ? 'confirm_sale_invoice.php' : 'confirm_invoice.php', {
                                method: 'POST',
                                body: createFormData({
                                    store_id: window.encryptedStoreId,
                                    account_id: window.encryptedAccountId,
                                    branch_id: branchId,
                                    account_buyer_id: accountBuyerId,
                                    items: addedItems,
                                    images: uploadedImagePaths.map(res => res.path)
                                })
                            });

                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }

                            const result = await response.json();

                            if (result.error) {
                                Swal.showValidationMessage(result.error === 'Incomplete data' ? 'بيانات غير مكتملة. يرجى التأكد من جميع البيانات المطلوبة.' : result.error);
                                return false;
                            }

                            return result;
                        } catch (error) {
                            console.error('Error:', error);
                            Swal.showValidationMessage('حدث خطأ أثناء حفظ الفاتورة. يرجى المحاولة مرة أخرى.');
                            return false;
                        }
                    }
                }).then(result => {
                    if (result.isConfirmed && result.value) {
                        // Check if the result contains success
                        if (result.value.success) {
                            // تشغيل الصوت المناسب حسب نوع الفاتورة
                            if (window.playSound) {
                                if (invoiceType.value === 'customer_return') {
                                    console.log('تشغيل صوت مرتجع العملاء - الوضع العادي');
                                    window.playSound('return');
                                } else {
                                    window.playSound('success');
                                }
                            }

                            const invoiceTypeValue = invoiceType.value;
                            let successMessage = '';
                            let successTitle = '';

                            if (invoiceTypeValue === 'sale') {
                                successTitle = 'تم إنشاء فاتورة البيع بالجملة';
                                successMessage = 'تم إضافة فاتورة بيع جديدة بنجاح ';
                            } else {
                                successTitle = 'تم إنشاء فاتورة الشراء';
                                successMessage = 'تم إضافة فاتورة شراء جديدة بنجاح';
                            }

                            Swal.fire({
                                icon: 'success',
                                title: successTitle,
                                text: successMessage,
                                confirmButtonText: 'حسناً',
                                timer: 3000,
                                timerProgressBar: true
                            }).then(() => location.reload());
                        } else {
                            // تشغيل صوت الخطأ
                            if (window.playSound) {
                                window.playSound('error');
                            }
                            // Handle error case
                            Swal.fire({
                                icon: 'error',
                                title: 'فشل في حفظ الفاتورة',
                                text: result.value.error || 'حدث خطأ غير متوقع',
                                confirmButtonText: 'حسناً'
                            });
                        }
                    }
                });
            });

            // Toggle sale options visibility based on invoice type
            invoiceType.addEventListener('change', function() {
                // فحص الصلاحيات عند تغيير نوع الفاتورة
                if (this.value === 'purchase' && !canCreatePurchase) {
                    showErrorMessage('ليس لديك صلاحية', 'ليس لديك صلاحية لإنشاء فواتير الشراء');
                    // إعادة تعيين القيمة السابقة
                    this.value = canCreateWholesale ? 'sale' : '';
                    return;
                }

                if ((this.value === 'sale' || this.value === 'customer_sale' || this.value === 'customer_return') && !canCreateWholesale) {
                    showErrorMessage('ليس لديك صلاحية', 'ليس لديك صلاحية لإنشاء فواتير البيع');
                    // إعادة تعيين القيمة السابقة
                    this.value = canCreatePurchase ? 'purchase' : '';
                    return;
                }

                if (this.value === 'sale') {
                    saleOptions.style.display = 'block';
                    customerSaleOptions.style.display = 'none';
                } else if (this.value === 'customer_sale' || this.value === 'customer_return') {
                    saleOptions.style.display = 'none';
                    customerSaleOptions.style.display = 'block';
                } else {
                    saleOptions.style.display = 'none';
                    customerSaleOptions.style.display = 'none';
                }
                saveItemsToFile();
            });

            branchSelect.addEventListener('change', saveItemsToFile);
            buyerSelect.addEventListener('change', saveItemsToFile);
            customerSelect.addEventListener('change', saveItemsToFile);

            // Ensure correct visibility on page load
            if (invoiceType.value === 'sale') {
                saleOptions.style.display = 'block';
                customerSaleOptions.style.display = 'none';
            } else if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                saleOptions.style.display = 'none';
                customerSaleOptions.style.display = 'block';
            } else {
                saleOptions.style.display = 'none';
                customerSaleOptions.style.display = 'none';
            }

            // تحديد القيمة الافتراضية بناءً على الصلاحيات
            let defaultInvoiceType = window.savedInvoiceType || '';

            // التحقق من صحة النوع المحفوظ مع الصلاحيات
            if (defaultInvoiceType === 'purchase' && !canCreatePurchase) {
                defaultInvoiceType = canCreateWholesale ? 'sale' : '';
            } else if ((defaultInvoiceType === 'sale' || defaultInvoiceType === 'customer_sale' || defaultInvoiceType === 'customer_return') && !canCreateWholesale) {
                defaultInvoiceType = canCreatePurchase ? 'purchase' : '';
            }

            // إذا لم يكن هناك نوع محفوظ، اختر الأول المتاح
            if (!defaultInvoiceType) {
                if (canCreatePurchase) {
                    defaultInvoiceType = 'purchase';
                } else if (canCreateWholesale) {
                    defaultInvoiceType = 'sale';
                }
            }

            // Restore saved invoice type, branch, and buyer
            invoiceType.value = defaultInvoiceType;
            branchSelect.value = window.savedBranchId || '';
            buyerSelect.value = window.savedAccountBuyerId || '';
            customerSelect.value = window.savedAccountBuyerId || '';

            // Toggle options visibility based on restored invoice type
            if (invoiceType.value === 'sale') {
                saleOptions.style.display = 'block';
                customerSaleOptions.style.display = 'none';
            } else if (invoiceType.value === 'customer_sale' || invoiceType.value === 'customer_return') {
                saleOptions.style.display = 'none';
                customerSaleOptions.style.display = 'block';
            } else {
                saleOptions.style.display = 'none';
                customerSaleOptions.style.display = 'none';
            }

            // Handle search functionality
            const searchInput = document.getElementById('search-input');
            // وظيفة تنظيف النص للبحث الأفضل والأكثر شمولية
            function normalizeText(text) {
                return text.toLowerCase()
                    // تطبيع الأحرف العربية
                    .replace(/[أإآ]/g, 'ا')
                    .replace(/[ة]/g, 'ه')
                    .replace(/[ى]/g, 'ي')
                    .replace(/[ء]/g, '')
                    // إزالة التشكيل
                    .replace(/[\u064B-\u0652]/g, '')
                    // تطبيع الأرقام العربية والإنجليز��ة
                    .replace(/[٠-٩]/g, (match) => String.fromCharCode(match.charCodeAt(0) - '٠'.charCodeAt(0) + '0'.charCodeAt(0)))
                    // إزالة علامات الترقيم والرموز
                    .replace(/[^\w\s\u0600-\u06FF]/g, ' ')
                    // إزالة المسافات الزائدة
                    .replace(/\s+/g, ' ')
                    .trim();
            }

            // وظيفة البحث الذكي مع نظام النقاط محسن
            function calculateSearchScore(itemName, searchQuery) {
                const normalizedItem = normalizeText(itemName);
                const normalizedQuery = normalizeText(searchQuery);

                if (normalizedQuery === '') return 0;

                let score = 0;
                const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0);
                const itemWords = normalizedItem.split(' ').filter(word => word.length > 0);

                // مطابقة تامة للنص كاملاً (أعلى أولوية)
                if (normalizedItem === normalizedQuery) {
                    return 1000;
                }

                // مطابقة في بداية النص (أولوية عالية جداً)
                if (normalizedItem.startsWith(normalizedQuery)) {
                    return 900;
                }

                // مطابقة جزئية للنص كاملاً
                if (normalizedItem.includes(normalizedQuery)) {
                    score += 500;
                }

                // حساب نقاط الكلمات
                let wordMatches = 0;
                let exactWordMatches = 0;

                queryWords.forEach(queryWord => {
                    let bestWordScore = 0;
                    let wordFound = false;

                    itemWords.forEach((itemWord, index) => {
                        let wordScore = 0;

                        // مطابقة تامة للكلمة (أعلى نقاط)
                        if (itemWord === queryWord) {
                            wordScore = 200;
                            exactWordMatches++;
                            wordFound = true;
                        }
                        // مطابقة في بداية الكلمة
                        else if (itemWord.startsWith(queryWord)) {
                            wordScore = 150;
                            wordFound = true;
                        }
                        // مطابقة في نهاية الكلمة
                        else if (itemWord.endsWith(queryWord)) {
                            wordScore = 100;
                            wordFound = true;
                        }
                        // مطابقة جزئية في وسط الكلمة
                        else if (itemWord.includes(queryWord)) {
                            wordScore = 80;
                            wordFound = true;
                        }

                        // نقاط إضافية للكلمة الأولى
                        if (index === 0 && wordScore > 0) {
                            wordScore += 50;
                        }

                        // نقاط إضافية للكلمة الثانية
                        if (index === 1 && wordScore > 0) {
                            wordScore += 25;
                        }

                        bestWordScore = Math.max(bestWordScore, wordScore);
                    });

                    if (wordFound) {
                        wordMatches++;
                        score += bestWordScore;
                    }
                });

                // مكافأة للمطابقات المتعددة
                if (wordMatches > 1) {
                    score += wordMatches * 30;
                }

                // مكافأة إضافية للمطابقات التامة
                if (exactWordMatches > 0) {
                    score += exactWordMatches * 100;
                }

                // مكافأة للنصوص القصيرة (أكثر دقة)
                if (itemWords.length <= 3 && wordMatches > 0) {
                    score += 50;
                }

                // مكافأة إذا كانت نسبة المطابقة عالية
                const matchRatio = wordMatches / queryWords.length;
                if (matchRatio >= 0.8) {
                    score += 100;
                } else if (matchRatio >= 0.5) {
                    score += 50;
                }

                return score;
            }

            // وظيفة البحث الضبابي (Fuzzy Search)
            function fuzzyMatch(text, pattern) {
                const textNorm = normalizeText(text);
                const patternNorm = normalizeText(pattern);

                if (patternNorm.length === 0) return 0;
                if (textNorm.length === 0) return -1;

                let score = 0;
                let textIndex = 0;

                for (let i = 0; i < patternNorm.length; i++) {
                    const char = patternNorm[i];
                    let found = false;

                    // البحث عن الحرف في النص
                    for (let j = textIndex; j < textNorm.length; j++) {
                        if (textNorm[j] === char) {
                            score += (textNorm.length - j); // نقاط أعلى للأحرف الأقرب للبداية
                            textIndex = j + 1;
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        score -= 10; // خصم نقاط للأحرف غير الموجودة
                    }
                }

                return score;
            }

            // وظيفة اقتراح كلمات مشابهة
            function getSuggestions(query, allItems) {
                const suggestions = [];
                const normalizedQuery = normalizeText(query);

                allItems.forEach(item => {
                    const itemName = item.getAttribute('data-item-name');
                    if (itemName) {
                        const words = normalizeText(itemName).split(' ');
                        words.forEach(word => {
                            if (word.length > 2 && word.includes(normalizedQuery.substring(0, 2))) {
                                if (!suggestions.includes(word) && suggestions.length < 5) {
                                    suggestions.push(word);
                                }
                            }
                        });
                    }
                });

                return suggestions;
            }

            let searchTimeout;
            const searchIcon = document.querySelector('.search-icon');
            const searchLoading = document.querySelector('.search-loading');

            searchInput.addEventListener('input', function() {
                        // إلغاء البحث السابق لتحسين الأداء
                        clearTimeout(searchTimeout);

                        // إظهار مؤشر التحميل
                        if (this.value.trim() !== '') {
                            searchIcon.style.display = 'none';
                            searchLoading.style.display = 'block';
                        }

                        searchTimeout = setTimeout(() => {
                                    const query = this.value.trim();
                                    const rows = Array.from(document.querySelectorAll('.custom-table tbody tr:not(.no-results-row)'));

                                    if (query === '') {
                                        // إظهار الصفوف حسب الفلتر الحالي إذا كان البحث فارغ
                                        applyFilter(currentFilter);
                                    } else {
                                        // حساب النقاط لكل صف
                                        const scoredRows = [];

                                        rows.forEach(row => {
                                            const itemName = row.getAttribute('data-item-name');

                                            if (itemName) {
                                                // حساب نقاط البحث الذكي
                                                const smartScore = calculateSearchScore(itemName, query);
                                                // حساب نقاط البحث الضبابي
                                                const fuzzyScore = fuzzyMatch(itemName, query);

                                                // النقاط الإجمالية
                                                const totalScore = smartScore + (fuzzyScore > 0 ? fuzzyScore * 0.3 : 0);

                                                scoredRows.push({
                                                    row: row,
                                                    score: totalScore,
                                                    name: itemName
                                                });
                                            }
                                        });

                                        // ترتيب النتائج حسب النقاط (من الأعلى إلى الأقل)
                                        scoredRows.sort((a, b) => b.score - a.score);

                                        // طباعة النقاط للمراقبة (يمكن حذفها لاحقاً)
                                        if (query.length > 0) {
                                            console.log('نتائج البحث لـ "' + query + '":');
                                            scoredRows.slice(0, 5).forEach((item, index) => {
                                                console.log(`${index + 1}. ${item.name} - النقاط: ${item.score}`);
                                            });
                                        }

                                        // إظهار النتائج المرتبة مع مراعاة الفلتر الحالي
                                        let visibleCount = 0;
                                        scoredRows.forEach((item, index) => {
                                            const row = item.row;
                                            const itemId = row.getAttribute('data-item-id');

                                            if (!itemId) {
                                                row.style.display = 'none';
                                                return;
                                            }

                                            // تطبيق الفلتر الحالي
                                            const imageCount = parseInt(row.getAttribute('data-image-count') || 0);
                                            const isFavorite = row.getAttribute('data-is-favorite') === '1';
                                            let passesFilter = true;

                                            switch (currentFilter) {
                                                case 'favorites':
                                                    passesFilter = isFavorite;
                                                    break;
                                                case 'with-images':
                                                    passesFilter = imageCount > 0;
                                                    break;
                                                case 'no-images':
                                                    passesFilter = imageCount === 0;
                                                    break;
                                                case 'all':
                                                default:
                                                    passesFilter = true;
                                                    break;
                                            }

                                            // إظهار النتائج التي تمر الفلتر ولها نقاط كافية
                                            if (item.score > 50 && passesFilter) {
                                                row.style.display = '';
                                                row.style.order = index;
                                                visibleCount++;

                                                // إضافة تأثير بصري للنتائج عالية النقاط
                                                if (item.score > 800) {
                                                    row.classList.add('high-score-result');
                                                } else {
                                                    row.classList.remove('high-score-result');
                                                }
                                            } else {
                                                row.style.display = 'none';
                                                row.classList.remove('high-score-result');
                                            }
                                        });

                                        // إخفاء الصفوف التي لا تحتوي على ��سماء منتجات
                                        rows.forEach(row => {
                                            if (!row.getAttribute('data-item-name')) {
                                                row.style.display = 'none';
                                            }
                                        });
                                        // إعادة ترتيب الصفوف في DOM للحصول على ترتيب صحيح
                                        if (query !== '') {
                                            const tbody = document.querySelector('.custom-table tbody');
                                            const visibleRows = scoredRows.filter(item => item.score > 50);
                                            visibleRows.forEach((item, index) => {
                                                tbody.appendChild(item.row);
                                            });
                                        }
                                    }

                                    // إضافة تأثير بصري للبحث
                                    if (query !== '') {
                                        searchInput.classList.add('searching');
                                    } else {
                                        searchInput.classList.remove('searching');
                                    }

                                    // عد النتائج المرئية
                                    const visibleRows = document.querySelectorAll('.custom-table tbody tr[style=""], .custom-table tbody tr:not([style*="none"]):not(.no-results-row)');
                                    const hasResults = visibleRows.length > 0 && Array.from(visibleRows).some(row => row.getAttribute('data-item-name'));

                                    // إضافة مؤشر لعدم وجود نتائج مع اقتراحات
                                    let noResultsRow = document.querySelector('.no-results-row');
                                    if (!hasResults && query !== '') {
                                        if (!noResultsRow) {
                                            // الحصول على اقتراحات
                                            const allRows = document.querySelectorAll('.custom-table tbody tr[data-item-name]');
                                            const suggestions = getSuggestions(query, allRows);

                                            let suggestionsHtml = '';
                                            if (suggestions.length > 0) {
                                                suggestionsHtml = `
                                <div style="margin-top: 15px;">
                                    <div style="font-size: 0.9rem; margin-bottom: 8px; color: var(--primary-color);">اقتراحات:</div>
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px; justify-content: center;">
                                        ${suggestions.map(suggestion => 
                                            `<span class="suggestion-tag" style="background: var(--primary-color); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; cursor: pointer;" onclick="document.getElementById('search-input').value='${suggestion}'; document.getElementById('search-input').dispatchEvent(new Event('input'));">${suggestion}</span>`
                                        ).join('')}
                                    </div>
                                </div>
                            `;
                        }
                        
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="4" class="text-center" style="padding: 40px 20px; color: var(--secondary-color);">
                                <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;"></i>
                                <div style="font-size: 1.1rem; font-weight: 600;">لا توجد نتائج للبحث "${query}"</div>
                                <div style="font-size: 0.9rem; margin-top: 5px;">جرب كلمات مختلفة أو تأكد من الإملاء</div>
                                ${suggestionsHtml}
                                <div style="margin-top: 15px; font-size: 0.8rem; color: var(--info-color);">
                                    💡 نصيحة: جرب البحث بجزء من اسم المنتج أو استخدم كلمات مفتاحية
                                </div>
                            </td>
                        `;
                        document.querySelector('.custom-table tbody').appendChild(noResultsRow);
                    }
                } else if (noResultsRow) {
                    noResultsRow.remove();
                }

                // إخفاء مؤشر التحميل وإظهار أيقونة البحث
                searchLoading.style.display = 'none';
                searchIcon.style.display = 'block';
                
                // تحديث إحصائيات البحث
                const finalVisibleRows = document.querySelectorAll('.custom-table tbody tr[style=""], .custom-table tbody tr:not([style*="none"]):not(.no-results-row)');
                const finalHasResults = Array.from(finalVisibleRows).some(row => row.getAttribute('data-item-name'));
                updateSearchStats(query, finalHasResults);
                
                }, 150); // تأخير 150ms لتحسين الأداء
            });

            // إضافة وظيفة البحث عند الضغط على Enter
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    
                    // البحث عن أول منتج ظاهر والتركيز عليه
                    const visibleRows = document.querySelectorAll('.custom-table tbody tr[style=""], .custom-table tbody tr:not([style*="none"]):not(.no-results-row)');
                    const validRows = Array.from(visibleRows).filter(row => row.getAttribute('data-item-name'));
                    
                    if (validRows.length > 0) {
                        const firstVisibleRow = validRows[0];
                        const addButton = firstVisibleRow.querySelector('.add-btn');
                        if (addButton) {
                            // إضافة تأثير بصري مؤقت
                            firstVisibleRow.style.backgroundColor = 'rgba(0, 123, 255, 0.15)';
                            firstVisibleRow.style.transform = 'scale(1.02)';
                            firstVisibleRow.style.boxShadow = '0 4px 15px rgba(0, 123, 255, 0.2)';
                            
                            setTimeout(() => {
                                firstVisibleRow.style.backgroundColor = '';
                                firstVisibleRow.style.transform = '';
                                firstVisibleRow.style.boxShadow = '';
                                addButton.focus();
                            }, 300);
                            
                            // إضافة إحصائية البحث
                            const itemName = firstVisibleRow.getAttribute('data-item-name');
                            console.log(`تم اختيار: ${itemName} من ${validRows.length} نتيجة`);
                        }
                    } else {
                        // إذا لم توجد نتائج، اقترح بحث أوسع
                        const currentQuery = this.value.trim();
                        if (currentQuery.length > 3) {
                            const shorterQuery = currentQuery.substring(0, Math.ceil(currentQuery.length * 0.7));
                            this.value = shorterQuery;
                            this.dispatchEvent(new Event('input'));
                        }
                    }
                }
                
                // التنقل بين النتائج بالأسهم
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    const visibleRows = Array.from(document.querySelectorAll('.custom-table tbody tr[style=""], .custom-table tbody tr:not([style*="none"]):not(.no-results-row)'))
                        .filter(row => row.getAttribute('data-item-name'));
                    
                    if (visibleRows.length > 0) {
                        let currentIndex = visibleRows.findIndex(row => row.classList.contains('keyboard-selected'));
                        
                        // إزالة التحديد السابق
                        visibleRows.forEach(row => {
                            row.classList.remove('keyboard-selected');
                            row.style.backgroundColor = '';
                        });
                        
                        // تحديد الصف الجديد
                        if (e.key === 'ArrowDown') {
                            currentIndex = (currentIndex + 1) % visibleRows.length;
                        } else {
                            currentIndex = currentIndex <= 0 ? visibleRows.length - 1 : currentIndex - 1;
                        }
                        
                        const selectedRow = visibleRows[currentIndex];
                        selectedRow.classList.add('keyboard-selected');
                        selectedRow.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                        selectedRow.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                }
            });

            // إضافة وظيفة مسح البحث عند الضغط على Escape
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // إزالة التحديد بلوحة المفاتيح
                    document.querySelectorAll('.keyboard-selected').forEach(row => {
                        row.classList.remove('keyboard-selected');
                        row.style.backgroundColor = '';
                    });
                    
                    this.value = '';
                    this.dispatchEvent(new Event('input'));
                    this.blur();
                }
            });

            // إضافة ميزة البحث السريع بالاختصارات
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + F للتركيز على البحث
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.select();
                }
                
                // Ctrl/Cmd + K للبحث السريع
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.select();
                }
            });

            // إضافة ميزة حفظ تاريخ البحث
            let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            
            function addToSearchHistory(query) {
                if (query.trim() && !searchHistory.includes(query)) {
                    searchHistory.unshift(query);
                    searchHistory = searchHistory.slice(0, 10); // الاحتفاظ بآخر 10 عمليات بحث
                    localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
                }
            }

            // إضافة ميزة الإكمال التلقائي
            let autocompleteTimeout;
            searchInput.addEventListener('focus', function() {
                showSearchSuggestions();
            });

            function showSearchSuggestions() {
                const currentValue = searchInput.value.toLowerCase().trim();
                let suggestions = [];
                
                // إضافة تاريخ البحث
                if (currentValue === '') {
                    suggestions = searchHistory.slice(0, 5);
                } else {
                    // البحث في تاريخ البحث
                    suggestions = searchHistory.filter(item => 
                        normalizeText(item).includes(normalizeText(currentValue))
                    ).slice(0, 3);
                    
                    // إضافة اقتراحات من أسماء المنتجات
                    const allRows = document.querySelectorAll('.custom-table tbody tr[data-item-name]');
                    const productSuggestions = [];
                    
                    allRows.forEach(row => {
                        const itemName = row.getAttribute('data-item-name');
                        if (itemName) {
                            const words = normalizeText(itemName).split(' ');
                            words.forEach(word => {
                                if (word.length > 2 && 
                                    normalizeText(word).includes(normalizeText(currentValue)) &&
                                    !suggestions.includes(word) &&
                                    !productSuggestions.includes(word)) {
                                    productSuggestions.push(word);
                                }
                            });
                        }
                    });
                    
                    suggestions = suggestions.concat(productSuggestions.slice(0, 5));
                }
                
                // إنشاء قائمة الاقتراحات
                createSuggestionDropdown(suggestions);
            }

            function createSuggestionDropdown(suggestions) {
                // إزالة القائمة السابقة
                const existingDropdown = document.querySelector('.search-suggestions');
                if (existingDropdown) {
                    existingDropdown.remove();
                }
                
                if (suggestions.length === 0) return;
                
                const dropdown = document.createElement('div');
                dropdown.className = 'search-suggestions';
                dropdown.style.cssText = `
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background: var(--white);
                    border: 2px solid var(--border-color);
                    border-top: none;
                    border-radius: 0 0 15px 15px;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    max-height: 200px;
                    overflow-y: auto;
                `;
                
                // تطبيق الثيم الداكن
                if (document.body.classList.contains('dark-mode')) {
                    dropdown.style.background = 'var(--dark-surface-light)';
                    dropdown.style.borderColor = 'var(--dark-border-light)';
                    dropdown.style.boxShadow = '0 4px 15px rgba(26, 35, 50, 0.3)';
                }
                
                suggestions.forEach((suggestion, index) => {
                    const item = document.createElement('div');
                    item.className = 'suggestion-item';
                    item.style.cssText = `
                        padding: 12px 15px;
                        cursor: pointer;
                        border-bottom: 1px solid var(--border-color);
                        transition: all 0.2s ease;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    `;
                    
                    // تحديد نوع الاقتراح
                    const isHistory = searchHistory.includes(suggestion);
                    const icon = isHistory ? 'fas fa-history' : 'fas fa-search';
                    const iconColor = isHistory ? 'var(--secondary-color)' : 'var(--primary-color)';
                    
                    item.innerHTML = `
                        <i class="${icon}" style="color: ${iconColor}; font-size: 0.9rem;"></i>
                        <span>${suggestion}</span>
                        ${isHistory ? '<i class="fas fa-times" style="margin-left: auto; color: var(--danger-color); font-size: 0.8rem;" onclick="removeFromHistory(event, \'' + suggestion + '\')"></i>' : ''}
                    `;
                    
                    // تطبيق الثيم الداكن
                    if (document.body.classList.contains('dark-mode')) {
                        item.style.borderBottomColor = 'var(--dark-border-light)';
                        item.style.color = 'var(--dark-text)';
                    }
                    
                    item.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                    });
                    
                    item.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                    
                    item.addEventListener('click', function(e) {
                        if (!e.target.classList.contains('fa-times')) {
                            searchInput.value = suggestion;
                            searchInput.dispatchEvent(new Event('input'));
                            dropdown.remove();
                            addToSearchHistory(suggestion);
                        }
                    });
                    
                    dropdown.appendChild(item);
                });
                
                searchInput.parentElement.appendChild(dropdown);
            }

            // وظيفة حذف من تاريخ البحث
            window.removeFromHistory = function(event, suggestion) {
                event.stopPropagation();
                searchHistory = searchHistory.filter(item => item !== suggestion);
                localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
                showSearchSuggestions();
            };

            // إخفاء الاقتراحات عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !e.target.closest('.search-suggestions')) {
                    const dropdown = document.querySelector('.search-suggestions');
                    if (dropdown) {
                        dropdown.remove();
                    }
                }
            });

            // إضافة إحصائيات البحث
            let searchStats = {
                totalSearches: 0,
                successfulSearches: 0,
                mostSearchedTerms: {}
            };

            function updateSearchStats(query, hasResults) {
                searchStats.totalSearches++;
                if (hasResults) {
                    searchStats.successfulSearches++;
                }
                
                if (query.trim()) {
                    searchStats.mostSearchedTerms[query] = (searchStats.mostSearchedTerms[query] || 0) + 1;
                    addToSearchHistory(query);
                }
                
                localStorage.setItem('searchStats', JSON.stringify(searchStats));
            }

            // تحميل إحصائيات البحث المحفوظة
            const savedStats = localStorage.getItem('searchStats');
            if (savedStats) {
                searchStats = JSON.parse(savedStats);
            }

            // إضافة ميزة البحث الصوتي (إذا كان متاحاً)
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();
                
                recognition.lang = 'ar-SA';
                recognition.continuous = false;
                recognition.interimResults = false;
                
                // إضافة زر البحث الصوتي
                const voiceButton = document.createElement('button');
                voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceButton.className = 'voice-search-btn';
                voiceButton.style.cssText = `
                    position: absolute;
                    left: 50px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: none;
                    border: none;
                    color: var(--secondary-color);
                    font-size: 16px;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                `;
                
                voiceButton.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                    this.style.color = 'var(--primary-color)';
                });
                
                voiceButton.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.color = 'var(--secondary-color)';
                });
                
                voiceButton.addEventListener('click', function() {
                    recognition.start();
                    this.style.color = 'var(--danger-color)';
                    this.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                });
                
                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    searchInput.value = transcript;
                    searchInput.dispatchEvent(new Event('input'));
                    voiceButton.style.color = 'var(--secondary-color)';
                    voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                };
                
                recognition.onerror = function() {
                    voiceButton.style.color = 'var(--secondary-color)';
                    voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                };
                
                searchInput.parentElement.appendChild(voiceButton);
            }

            // Image Modal Functions
            window.viewItemImages = function(itemId, itemName) {
                const modal = document.getElementById('imageModal');
                const modalTitle = document.getElementById('modalItemName');
                const modalContent = document.getElementById('modalImageContent');
                
                modalTitle.textContent = `صور الصنف: ${itemName}`;
                modalContent.innerHTML = '<div style="text-align: center; padding: 40px;"><i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-color);"></i><br><br>جاري تحميل الصور...</div>';
                
                modal.style.display = 'block';
                
                // Fetch images from server
                fetch(`get_item_images.php?item_id=${encodeURIComponent(itemId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (data.images && data.images.length > 0) {
                                let imagesHtml = '<div class="images-gallery">';
                                data.images.forEach(image => {
                                    imagesHtml += `
                                        <div class="image-item">
                                            <img src="${image.img_path}" 
                                                 alt="صورة الصنف" 
                                                 onclick="openFullsizeImage('${image.img_path}')"
                                                 onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\'padding: 20px; color: var(--danger-color);\\'>فشل في تحميل الصورة</div>';">
                                        </div>
                                    `;
                                });
                                imagesHtml += '</div>';
                                modalContent.innerHTML = imagesHtml;
                            } else {
                                modalContent.innerHTML = `
                                    <div class="no-images">
                                        <i class="fas fa-image"></i>
                                        <p>لا توجد صور لهذا الصنف</p>
                                    </div>
                                `;
                            }
                        } else {
                            modalContent.innerHTML = `
                                <div class="no-images">
                                    <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                                    <p style="color: var(--danger-color);">حدث خطأ في تحميل الصور</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching images:', error);
                        modalContent.innerHTML = `
                            <div class="no-images">
                                <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                                <p style="color: var(--danger-color);">حدث خطأ في الاتصال بالخادم</p>
                            </div>
                        `;
                    });
            };

            window.closeImageModal = function() {
                document.getElementById('imageModal').style.display = 'none';
            };

            window.openFullsizeImage = function(imageSrc) {
                const fullsizeModal = document.getElementById('fullsizeModal');
                const fullsizeImage = document.getElementById('fullsizeImage');
                
                fullsizeImage.src = imageSrc;
                fullsizeModal.style.display = 'block';
            };

            window.closeFullsizeModal = function() {
                document.getElementById('fullsizeModal').style.display = 'none';
            };

            // Close modals when clicking outside
            window.onclick = function(event) {
                const imageModal = document.getElementById('imageModal');
                const fullsizeModal = document.getElementById('fullsizeModal');
                
                if (event.target === imageModal) {
                    closeImageModal();
                }
                if (event.target === fullsizeModal) {
                    closeFullsizeModal();
                }
            };

            // Close modals with Escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeImageModal();
                    closeFullsizeModal();
                }
            });
        // ميزة قارئ الباركود - محسنة ومطابقة لـ invoice_responsive.js
            let barcodeBuffer = '';
            let barcodeTimeoutId = null;
            let barcodeItemsMap = {};

            // بناء خريطة الباركود عند تحميل الصفحة
            function buildBarcodeMap() {
                barcodeItemsMap = {};
                const itemRows = document.querySelectorAll('.custom-table tbody tr[data-item-barcode]');
                
                itemRows.forEach(row => {
                    const itemBarcode = row.getAttribute('data-item-barcode');
                    const itemId = row.getAttribute('data-item-id');
                    
                    if (itemBarcode && itemBarcode.trim() !== '' && itemId) {
                        barcodeItemsMap[itemBarcode.trim()] = itemId;
                        console.log('تم إضافة الباركود:', itemBarcode, 'للصنف:', row.getAttribute('data-item-name'));
                    }
                });
                
                console.log(`تم تحميل ${Object.keys(barcodeItemsMap).length} باركود للأصناف.`);
            }

            // بناء خريطة الباركود
            buildBarcodeMap();

            document.addEventListener('keydown', function(e) {
                // تجاهل الأحداث في عناصر الإدخال
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                    return;
                }

                // التحقق من وجود النظام الريسبونسيف لتجنب التداخل
                if (window.isResponsiveMode && window.isResponsiveMode()) {
                    return; // إذا كان النظام في الوضع الريسبونسيف، لا تعالج هنا
                }

                // الباركود عادة ما ينتهي بمفتاح Enter
                if (e.key === 'Enter' && barcodeBuffer.length >= 5) {
                    // معالجة الباركود المخصص
                    const customBarcodeData = processCustomBarcode(barcodeBuffer);
                    
                    if (customBarcodeData) {
                        console.log('تم اكتشاف باركود مخصص:', barcodeBuffer);
                        console.log('باركود الصنف:', customBarcodeData.itemBarcode);
                        console.log('الوزن بالجرام:', customBarcodeData.weightInGrams);
                        console.log('الكمية بالكيلو:', customBarcodeData.quantity);

                        // منع الحفظ المؤقت أثناء معالجة الباركود المخصص
                        window.processingCustomBarcode = true;

                        // البحث عن الصنف بالباركود المستخرج
                        const itemId = barcodeItemsMap[customBarcodeData.itemBarcode];
                        if (itemId) {
                            // العثور على الصف المقابل للصنف
                            const targetRow = document.querySelector(`tr[data-item-id="${itemId}"]`);
                            if (targetRow) {
                                const addButton = targetRow.querySelector('.add-btn');
                                const itemName = targetRow.getAttribute('data-item-name');
                                
                                if (addButton) {
                                    // إضافة الصنف مباشرة بالكمية الصحيحة بدلاً من النقر على الزر
                                    const itemPrice = targetRow.getAttribute('data-item-price');

                                    // التحقق من وجود الصنف مسبقاً
                                    const existingItemIndex = addedItems.findIndex(item => item.id === itemId);
                                    if (existingItemIndex !== -1) {
                                        // تحديث الصنف الموجود
                                        addedItems[existingItemIndex].quantity = customBarcodeData.quantity;
                                        addedItems[existingItemIndex].isCustomBarcode = true;
                                        addedItems[existingItemIndex].customBarcodeData = customBarcodeData;
                                        console.log('تم تحديث صنف موجود بباركود مخصص:', addedItems[existingItemIndex]);
                                    } else {
                                        // إضافة صنف جديد بالكمية الصحيحة مباشرة
                                        addedItems.push({
                                            id: itemId,
                                            name: itemName,
                                            quantity: customBarcodeData.quantity,
                                            price: itemPrice,
                                            isCustomBarcode: true,
                                            customBarcodeData: customBarcodeData
                                        });
                                        console.log('تم إضافة صنف جديد بباركود مخصص:', {
                                            name: itemName,
                                            quantity: customBarcodeData.quantity,
                                            price: itemPrice
                                        });
                                    }

                                    // تحديث واجهة المستخدم
                                    targetRow.classList.add('highlight');
                                    const nameCell = targetRow.querySelector('td:first-child');
                                    const cell = targetRow.querySelector('.add-cell');
                                    nameCell.innerHTML = `
                                        <div class="item-name">
                                            <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                                            ${itemName}
                                        </div>
                                    `;
                                    cell.innerHTML = `
                                        <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="${customBarcodeData.quantity.toFixed(3)}" style="width: 80px; margin: auto;">
                                    `;

                                    // تحديث عداد الأصناف
                                    itemCountElement.textContent = addedItems.length;
                                    const responsiveItemCount = document.getElementById('responsive-item-count');
                                    if (responsiveItemCount) {
                                        responsiveItemCount.textContent = addedItems.length;
                                    }

                                    // إضافة معالج أحداث للكمية
                                    const quantityInput = targetRow.querySelector('.quantity-input');
                                    if (quantityInput) {
                                        quantityInput.addEventListener('input', function() {
                                            const itemIndex = addedItems.findIndex(item => item.id === itemId);
                                            if (itemIndex !== -1) {
                                                addedItems[itemIndex].quantity = parseFloat(this.value) || 1;
                                            }
                                            saveItemsToFile();
                                        });
                                    }

                                    // السماح بالحفظ المؤقت مرة أخرى بعد انتهاء المعالجة
                                    window.processingCustomBarcode = false;
                                    console.log('تم الانتهاء من معالجة الباركود المخصص - يمكن الحفظ الآن');

                                    console.log('تمت إضافة الصنف بالباركود المخصص:', barcodeBuffer);
                                    console.log('الصنف:', itemName, 'الكمية:', customBarcodeData.quantity, 'كيلو');

                                    // حساب وعرض الإجمالي الصحيح
                                    const totalAmount = customBarcodeData.quantity * parseFloat(itemPrice);
                                    console.log('الإجمالي المحسوب:', totalAmount.toFixed(2), 'جنيه');

                                    // إظهار تنبيه بصري مخصص مع الإجمالي
                                    showBarcodeNotification(`تم إضافة ${itemName} - الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو - الإجمالي: ${totalAmount.toFixed(2)} جنيه`);

                                    // تمييز الصف المضاف
                                    highlightAddedItem(itemId);
                                }
                            }
                        } else {
                            console.log('باركود صنف غير معروف:', customBarcodeData.itemBarcode);

                            // تشغيل صوت الخطأ
                            playErrorSound();

                            // عرض إشعار للباركود غير المعروف
                            showBarcodeNotification(`باركود صنف غير معروف: ${customBarcodeData.itemBarcode}`, 'error');
                        }
                    } else {
                        // معالجة الباركود العادي
                        const itemId = barcodeItemsMap[barcodeBuffer];
                        if (itemId) {
                            // العثور على الصف المقابل للصنف
                            const targetRow = document.querySelector(`tr[data-item-id="${itemId}"]`);
                            if (targetRow) {
                                const addButton = targetRow.querySelector('.add-btn');
                                const itemName = targetRow.getAttribute('data-item-name');
                                
                                if (addButton) {
                                    // النقر على زر الإضافة
                                    addButton.click();
                                    
                                    console.log('تمت إضافة الصنف بالباركود:', barcodeBuffer);

                                    // إظهار تنبيه بصري
                                    showBarcodeNotification(`تم إضافة ${itemName}`);

                                    // تمييز الصف المضاف
                                    highlightAddedItem(itemId);
                                }
                            }
                        } else {
                            console.log('باركود غير معروف:', barcodeBuffer);

                            // تشغيل صوت الخطأ
                            playErrorSound();

                            // عرض إشعار للباركود غير المعروف
                            showBarcodeNotification('باركود غير معروف!', 'error');
                        }
                    }

                    // إعادة تعيين المخزن المؤقت
                    barcodeBuffer = '';
                    e.preventDefault();
                    return;
                }

                // إضافة الحرف إلى المخزن المؤقت وإعادة تعيين المؤقت
                if (e.key.length === 1 || e.key === '-' || e.key === '_') {
                    // قارئات الباركود عادة ما تكون سريعة جدًا في إدخال الأحرف
                    // إعادة تعيين المخزن المؤقت إذا كان هناك فارق زمني كبير بين الحروف
                    if (barcodeTimeoutId) {
                        clearTimeout(barcodeTimeoutId);
                    }

                    barcodeBuffer += e.key;

                    // إعادة تعيين المخزن المؤقت بعد 100 مللي ثانية من عدم النشاط
                    barcodeTimeoutId = setTimeout(() => {
                        barcodeBuffer = '';
                        barcodeTimeoutId = null;
                    }, 100);
                }
            });

            // دالة إظهار إشعار الباركود
            function showBarcodeNotification(message, type = 'success') {
                // إزالة الإشعارات السابقة
                const existingNotifications = document.querySelectorAll('.barcode-notification');
                existingNotifications.forEach(notification => notification.remove());

                // إنشاء إشعار جديد
                const notification = document.createElement('div');
                notification.className = `barcode-notification ${type === 'error' ? 'error' : ''}`;
                notification.textContent = message;
                
                // إضافة الإشعار للصفحة
                document.body.appendChild(notification);

                // إزالة الإشعار بعد 3 ثوان
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 3000);
            }

            // دالة لتحديد وحدة القياس بناءً على نوع الصنف
            function getQuantityUnit(itemType) {
                switch(itemType) {
                    case 'box': return ' كرتونة';
                    case 'fridge': return ' كيلو';
                    case 'piece': return ' قطعة';
                    case 'other': return ' قطعة';
                    default: return ' قطعة';
                }
            }

            // دالة معالجة الباركود المخصص - نسخة سطح المكتب
            function processCustomBarcode(barcode) {
                // تجنب التداخل مع النسخة المتجاوبة
                if (window.isResponsiveMode && window.isResponsiveMode()) {
                    return null; // دع النسخة المتجاوبة تتعامل مع الباركود
                }

                if (!barcode.startsWith('2') || barcode.length !== 13) {
                    return null;
                }

                // استخراج باركود الصنف (6 أرقام بعد الرقم 2)
                const itemBarcode = barcode.substring(1, 7);

                // استخراج الوزن بالجرام (5 أرقام من الموضع 7 إلى 12، الرقم 12 هو آخر رقم في الوزن)
                const weightString = barcode.substring(7, 12);
                const weightInGrams = parseInt(weightString);
                const quantityInKg = weightInGrams / 1000; // تحويل إلى كيلوجرام

                console.log('تفاصيل معالجة الباركود المخصص:');
                console.log('الباركود الكامل:', barcode);
                console.log('تحليل الباركود:');
                console.log('- الرقم 0:', barcode[0], '(نوع الباركود)');
                console.log('- الأرقام 1-6:', barcode.substring(1, 7), '(باركود الصنف)');
                console.log('- الأرقام 7-11:', barcode.substring(7, 12), '(الوزن)');
                console.log('- الرقم 12:', barcode[12], '(check digit)');
                console.log('باركود الصنف:', itemBarcode);
                console.log('نص الوزن المستخرج:', weightString);
                console.log('الوزن بالجرام:', weightInGrams);
                console.log('الكمية بالكيلو:', quantityInKg);

                return {
                    itemBarcode: itemBarcode,
                    quantity: quantityInKg,
                    weightInGrams: weightInGrams,
                    isCustomBarcode: true // إضافة علامة للتمييز
                };
            }

            // دالة تمييز الصف المضاف
            function highlightAddedItem(itemId) {
                const targetRow = document.querySelector(`tr[data-item-id="${itemId}"]`);
                if (targetRow) {
                    // إضافة تأثير بصري مؤقت
                    targetRow.style.backgroundColor = 'rgba(40, 167, 69, 0.2)';
                    targetRow.style.transform = 'scale(1.02)';
                    targetRow.style.boxShadow = '0 4px 15px rgba(40, 167, 69, 0.3)';
                    
                    setTimeout(() => {
                        targetRow.style.backgroundColor = '';
                        targetRow.style.transform = '';
                        targetRow.style.boxShadow = '';
                    }, 1000);
                }
            }
        });